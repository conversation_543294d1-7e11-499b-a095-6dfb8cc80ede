/**
 * WCAG-033: Audio-only and Video-only Check
 * Success Criterion: 1.2.1 Audio-only and Video-only (Prerecorded) (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';
import MultimediaAccessibilityTester, {
  MultimediaAccessibilityReport,
} from '../utils/multimedia-accessibility-tester';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';

interface MediaElement {
  type: 'audio' | 'video' | 'embed' | 'iframe';
  selector: string;
  src?: string;
  hasControls: boolean;
  hasAlternative: boolean;
  alternativeType?: 'transcript' | 'description' | 'captions' | 'none';
  alternativeText?: string;
  isDecorative: boolean;
  duration?: number;
  hasAudio: boolean;
  hasVideo: boolean;
  isPrerecorded: boolean;
}

interface AudioVideoAnalysis {
  audioOnlyElements: MediaElement[];
  videoOnlyElements: MediaElement[];
  totalMediaElements: number;
  elementsWithAlternatives: number;
  elementsWithoutAlternatives: number;
  decorativeElements: number;
}

export interface AudioVideoOnlyConfig extends EnhancedCheckConfig {
  enableMultimediaAccessibilityTesting?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAdvancedMediaAnalysis?: boolean;
}

export class AudioVideoOnlyCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private multimediaAccessibilityTester = MultimediaAccessibilityTester.getInstance();
  private contentQualityAnalyzer = new ContentQualityAnalyzer();

  // Keywords that indicate alternative content
  private readonly alternativeIndicators = {
    transcript: ['transcript', 'text version', 'full text', 'script'],
    description: ['description', 'audio description', 'described', 'narrative'],
    captions: ['captions', 'subtitles', 'closed captions', 'cc'],
  };

  // Keywords that indicate decorative content
  private readonly decorativeIndicators = [
    'background', 'ambient', 'decorative', 'atmosphere', 'mood',
    'music only', 'sound effect', 'notification', 'alert sound'
  ];

  async performCheck(config: AudioVideoOnlyConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: AudioVideoOnlyConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 3000,
      },
      enableMultimediaAccessibilityTesting: true,
      enableContentQualityAnalysis: true,
      enableAdvancedMediaAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-033',
      'Audio-only and Video-only',
      'perceivable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeAudioVideoOnlyCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with audio/video-only analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-033',
        ruleName: 'Audio-only and Video-only',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'audio-video-only-analysis',
          mediaElementAnalysis: true,
          alternativeContentDetection: true,
          decorativeContentDetection: true,
          multimediaAccessibilityTesting: enhancedConfig.enableMultimediaAccessibilityTesting,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 30,
      },
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'media-alternative-analysis',
        confidence: 0.65,
        additionalData: {
          checkType: 'media-accessibility',
          automationLevel: 'medium',
        },
      },
    };
  }

  private async executeAudioVideoOnlyCheck(
    page: Page,
    _config: AudioVideoOnlyConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced multimedia accessibility testing
    const multimediaReport =
      await this.multimediaAccessibilityTester.testMultimediaAccessibility(page);

    // Analyze audio-only and video-only elements using MultimediaAccessibilityTester results
    const audioOnlyAnalysis = await this.analyzeAudioOnlyElements(page, multimediaReport);
    const videoOnlyAnalysis = await this.analyzeVideoOnlyElements(page, multimediaReport);

    // Combine analyses
    const allAnalyses = [audioOnlyAnalysis, videoOnlyAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze audio-only elements using MultimediaAccessibilityTester
   */
  private async analyzeAudioOnlyElements(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const audioElements = multimediaReport.audioElements;
    const totalChecks = audioElements.length;
    let passedChecks = 0;

    audioElements.forEach((audio, index) => {
      // Check for transcript availability
      if (audio.transcript.hasTranscript && audio.transcript.isLinked) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Audio-only element ${index + 1} has accessible transcript`,
          value: `${audio.selector} - transcript: ${audio.transcript.location}`,
          selector: audio.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Audio-only element ${index + 1} lacks accessible transcript`);
        evidence.push({
          type: 'code',
          description: `Audio-only element ${index + 1} requires transcript`,
          value: `${audio.selector} - hasTranscript: ${audio.transcript.hasTranscript}`,
          selector: audio.selector,
          severity: 'error',
        });
        recommendations.push(`Add accessible transcript for audio-only element ${index + 1}`);
      }

      // Add issues from MultimediaAccessibilityTester
      if (audio.issues.length > 0) {
        audio.issues.forEach((issue) => {
          issues.push(`Audio ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (audio.recommendations.length > 0) {
        audio.recommendations.forEach((recommendation) => {
          recommendations.push(`Audio ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze video-only elements using MultimediaAccessibilityTester
   */
  private async analyzeVideoOnlyElements(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Filter video elements that are video-only (no audio)
    const videoOnlyElements = multimediaReport.videoElements.filter((video) => {
      // Check if video has no audio track or is muted
      return !video.audioDescription.hasAudioDescription;
    });

    const totalChecks = videoOnlyElements.length;
    let passedChecks = 0;

    videoOnlyElements.forEach((video, index) => {
      // Check for audio description or transcript
      if (
        video.audioDescription.hasAudioDescription ||
        video.transcript.hasTranscript ||
        video.captions.hasTextTracks
      ) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Video-only element ${index + 1} has accessible alternative`,
          value: `${video.selector} - audioDescription: ${video.audioDescription.hasAudioDescription}, transcript: ${video.transcript.hasTranscript}`,
          selector: video.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Video-only element ${index + 1} lacks accessible alternative`);
        evidence.push({
          type: 'code',
          description: `Video-only element ${index + 1} requires audio description or transcript`,
          value: `${video.selector} - no accessible alternatives found`,
          selector: video.selector,
          severity: 'error',
        });
        recommendations.push(
          `Add audio description or transcript for video-only element ${index + 1}`,
        );
      }

      // Add issues from MultimediaAccessibilityTester
      if (video.issues.length > 0) {
        video.issues.forEach((issue) => {
          issues.push(`Video ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (video.recommendations.length > 0) {
        video.recommendations.forEach((recommendation) => {
          recommendations.push(`Video ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Legacy media analysis method (kept for reference)
   */
  private async analyzeLegacyMediaElements(page: Page): Promise<AudioVideoAnalysis> {
    const startTime = Date.now();

    // Analyze media elements on the page
    const mediaAnalysis = await page.evaluate(() => {
      const audioElements = Array.from(document.querySelectorAll('audio'));
      const videoElements = Array.from(document.querySelectorAll('video'));
      const embedElements = Array.from(document.querySelectorAll('embed'));
      const iframeElements = Array.from(document.querySelectorAll('iframe'));

      const audioOnlyElements: MediaElement[] = [];
      const videoOnlyElements: MediaElement[] = [];

    const alternativeIndicators = {
      transcript: ['transcript', 'text version', 'full text', 'script'],
      description: ['description', 'audio description', 'described version'],
      captions: ['captions', 'subtitles', 'closed captions', 'cc']
    };

    const decorativeIndicators = ['background', 'ambient', 'decoration', 'decorative'];

    function findAlternativeContent(element: Element): {
        hasAlternative: boolean;
        alternativeType?: 'transcript' | 'description' | 'captions' | 'none';
        alternativeText?: string;
      } {
        const parent = element.parentElement;
        const container = element.closest('section, article, div, figure') || parent;
        
        if (!container) {
          return { hasAlternative: false, alternativeType: 'none' };
        }

        const containerText = container.textContent?.toLowerCase() || '';
        const containerHTML = container.innerHTML.toLowerCase();

        // Check for transcript
        for (const indicator of alternativeIndicators.transcript) {
          if (containerText.includes(indicator) || containerHTML.includes(indicator)) {
            return {
              hasAlternative: true,
              alternativeType: 'transcript',
              alternativeText: container.textContent?.trim().substring(0, 200)
            };
          }
        }

        // Check for description
        for (const indicator of alternativeIndicators.description) {
          if (containerText.includes(indicator) || containerHTML.includes(indicator)) {
            return {
              hasAlternative: true,
              alternativeType: 'description',
              alternativeText: container.textContent?.trim().substring(0, 200)
            };
          }
        }

        // Check for captions
        for (const indicator of alternativeIndicators.captions) {
          if (containerText.includes(indicator) || containerHTML.includes(indicator)) {
            return {
              hasAlternative: true,
              alternativeType: 'captions',
              alternativeText: container.textContent?.trim().substring(0, 200)
            };
          }
        }

        // Check for track elements (captions/subtitles)
        const trackElements = element.querySelectorAll('track');
        if (trackElements.length > 0) {
          return {
            hasAlternative: true,
            alternativeType: 'captions',
            alternativeText: `${trackElements.length} track element(s) found`
          };
        }

        return { hasAlternative: false, alternativeType: 'none' };
      }

      // Helper function to check if content is decorative
      function isDecorative(element: Element): boolean {
        const elementText = element.textContent?.toLowerCase() || '';
        const parentText = element.parentElement?.textContent?.toLowerCase() || '';
        const ariaHidden = element.getAttribute('aria-hidden') === 'true';
        const role = element.getAttribute('role');

        if (ariaHidden || role === 'presentation') {
          return true;
        }

        return decorativeIndicators.some(indicator => 
          elementText.includes(indicator) || parentText.includes(indicator)
        );
      }

      // Analyze audio elements
      audioElements.forEach((audio, index) => {
        const alternative = findAlternativeContent(audio);
        const isDecorativeContent = isDecorative(audio);
        
        audioOnlyElements.push({
          type: 'audio',
          selector: `audio:nth-of-type(${index + 1})`,
          src: audio.getAttribute('src') || audio.querySelector('source')?.getAttribute('src') || undefined,
          hasControls: audio.hasAttribute('controls'),
          hasAlternative: alternative.hasAlternative,
          alternativeType: alternative.alternativeType,
          alternativeText: alternative.alternativeText,
          isDecorative: isDecorativeContent,
          hasAudio: true,
          hasVideo: false,
          isPrerecorded: !audio.hasAttribute('live'), // Assume prerecorded unless marked as live
        });
      });

      // Analyze video elements (video-only content)
      videoElements.forEach((video, index) => {
        const alternative = findAlternativeContent(video);
        const isDecorativeContent = isDecorative(video);
        
        // Check if video has audio track (this is limited in browser context)
        const hasAudioTrack = video.querySelector('track[kind="audio"]') !== null ||
                             !video.hasAttribute('muted');

        // Only consider as video-only if it appears to have no audio
        if (video.hasAttribute('muted') || video.textContent?.toLowerCase().includes('no audio')) {
          videoOnlyElements.push({
            type: 'video',
            selector: `video:nth-of-type(${index + 1})`,
            src: video.getAttribute('src') || video.querySelector('source')?.getAttribute('src') || undefined,
            hasControls: video.hasAttribute('controls'),
            hasAlternative: alternative.hasAlternative,
            alternativeType: alternative.alternativeType,
            alternativeText: alternative.alternativeText,
            isDecorative: isDecorativeContent,
            hasAudio: false,
            hasVideo: true,
            isPrerecorded: !video.hasAttribute('live'),
          });
        }
      });

      // Analyze embed elements
      embedElements.forEach((embed, index) => {
        const type = embed.getAttribute('type')?.includes('audio') ? 'audio' : 'video';
        const alternative = findAlternativeContent(embed);
        const isDecorativeContent = isDecorative(embed);

        if (type === 'audio') {
          audioOnlyElements.push({
            type: 'embed',
            selector: `embed:nth-of-type(${index + 1})`,
            src: embed.getAttribute('src') || undefined,
            hasControls: true, // Assume embed has controls
            hasAlternative: alternative.hasAlternative,
            alternativeType: alternative.alternativeType,
            alternativeText: alternative.alternativeText,
            isDecorative: isDecorativeContent,
            hasAudio: true,
            hasVideo: false,
            isPrerecorded: true,
          });
        }
      });

      // Analyze iframe elements (external media)
      iframeElements.forEach((iframe, index) => {
        const src = iframe.getAttribute('src') || '';
        const alternative = findAlternativeContent(iframe);
        const isDecorativeContent = isDecorative(iframe);
        
        // Determine if it's audio or video based on source
        const isAudio = src.includes('soundcloud') || src.includes('spotify');
        
        if (isAudio) {
          audioOnlyElements.push({
            type: 'iframe',
            selector: `iframe:nth-of-type(${index + 1})`,
            src,
            hasControls: true,
            hasAlternative: alternative.hasAlternative,
            alternativeType: alternative.alternativeType,
            alternativeText: alternative.alternativeText,
            isDecorative: isDecorativeContent,
            hasAudio: true,
            hasVideo: false,
            isPrerecorded: true,
          });
        }
      });

      const totalMediaElements = audioOnlyElements.length + videoOnlyElements.length;
      const allElements = [...audioOnlyElements, ...videoOnlyElements];
      const elementsWithAlternatives = allElements.filter(el => el.hasAlternative).length;
      const elementsWithoutAlternatives = allElements.filter(el => !el.hasAlternative && !el.isDecorative).length;
      const decorativeElements = allElements.filter(el => el.isDecorative).length;

      return {
        audioOnlyElements,
        videoOnlyElements,
        totalMediaElements,
        elementsWithAlternatives,
        elementsWithoutAlternatives,
        decorativeElements,
      };
    });

    return mediaAnalysis;
  }

  /**
   * Process legacy media analysis results
   */
  private async processLegacyMediaAnalysis(
    mediaAnalysis: AudioVideoAnalysis,
    startTime: number
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = mediaAnalysis.totalMediaElements;

    if (totalElements === 0) {
      // No media elements found
      evidence.push({
        type: 'info',
        description: 'No audio-only or video-only content found',
        value: 'Page contains no prerecorded audio-only or video-only media elements',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalMediaElements: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future media content includes appropriate alternatives'],
      };
    }

    // Analyze elements without alternatives (excluding decorative)
    if (mediaAnalysis.elementsWithoutAlternatives > 0) {
      const failureRate = mediaAnalysis.elementsWithoutAlternatives / 
                         (totalElements - mediaAnalysis.decorativeElements);
      score = Math.max(0, Math.round(100 * (1 - failureRate)));

      // Check audio-only elements without alternatives
      mediaAnalysis.audioOnlyElements
        .filter(element => !element.hasAlternative && !element.isDecorative)
        .forEach((element) => {
          issues.push(`Audio-only content without text alternative: ${element.selector}`);
          
          evidence.push({
            type: 'error',
            description: 'Audio-only content missing text alternative',
            value: `Audio element without transcript or text alternative`,
            selector: element.selector,
            elementCount: 1,
            affectedSelectors: [element.selector],
            severity: 'error',
            fixExample: {
              before: '<audio controls src="podcast.mp3"></audio>',
              after: `<audio controls src="podcast.mp3"></audio>
<div class="transcript">
  <h3>Transcript</h3>
  <p>Full text transcript of the audio content...</p>
</div>`,
              description: 'Provide text transcript for audio-only content',
              codeExample: `
<!-- Option 1: Transcript -->
<audio controls src="interview.mp3" aria-describedby="transcript"></audio>
<div id="transcript">
  <h3>Interview Transcript</h3>
  <p><strong>Host:</strong> Welcome to our show...</p>
  <p><strong>Guest:</strong> Thank you for having me...</p>
</div>

<!-- Option 2: Link to transcript -->
<audio controls src="lecture.mp3"></audio>
<p><a href="lecture-transcript.html">Read full transcript</a></p>
              `,
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/audio-only-and-video-only-prerecorded.html',
                'https://webaim.org/techniques/captions/',
                'https://www.w3.org/WAI/media/av/transcripts/'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                mediaType: 'audio-only',
                elementType: element.type,
                hasControls: element.hasControls,
                src: element.src || 'unknown',
                isPrerecorded: element.isPrerecorded,
              },
            },
          });
        });

      // Check video-only elements without alternatives
      mediaAnalysis.videoOnlyElements
        .filter(element => !element.hasAlternative && !element.isDecorative)
        .forEach((element) => {
          issues.push(`Video-only content without text alternative: ${element.selector}`);
          
          evidence.push({
            type: 'error',
            description: 'Video-only content missing text alternative',
            value: `Video element without audio description or text alternative`,
            selector: element.selector,
            elementCount: 1,
            affectedSelectors: [element.selector],
            severity: 'error',
            fixExample: {
              before: '<video controls src="animation.mp4" muted></video>',
              after: `<video controls src="animation.mp4" muted></video>
<div class="description">
  <h3>Video Description</h3>
  <p>Detailed description of the visual content...</p>
</div>`,
              description: 'Provide text description for video-only content',
              codeExample: `
<!-- Option 1: Text description -->
<video controls src="silent-demo.mp4" muted aria-describedby="video-desc"></video>
<div id="video-desc">
  <h3>Video Description</h3>
  <p>The video shows a step-by-step demonstration of...</p>
</div>

<!-- Option 2: Audio description track -->
<video controls src="animation.mp4" muted>
  <track kind="descriptions" src="animation-desc.vtt" srclang="en" label="Audio descriptions">
</video>
              `,
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/audio-only-and-video-only-prerecorded.html',
                'https://www.w3.org/WAI/media/av/description/'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                mediaType: 'video-only',
                elementType: element.type,
                hasControls: element.hasControls,
                src: element.src || 'unknown',
                isPrerecorded: element.isPrerecorded,
              },
            },
          });
        });
    }

    // Add positive evidence for elements with alternatives
    if (mediaAnalysis.elementsWithAlternatives > 0) {
      evidence.push({
        type: 'info',
        description: 'Media elements with alternatives found',
        value: `${mediaAnalysis.elementsWithAlternatives} media elements have text alternatives`,
        selector: 'audio, video',
        elementCount: mediaAnalysis.elementsWithAlternatives,
        affectedSelectors: ['audio', 'video'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: mediaAnalysis.elementsWithAlternatives,
          checkSpecificData: {
            elementsWithAlternatives: mediaAnalysis.elementsWithAlternatives,
          },
        },
      });
    }

    // Add summary evidence
    evidence.push({
      type: score >= 80 ? 'info' : 'warning',
      description: 'Audio/video-only content analysis summary',
      value: `${totalElements} media elements: ${mediaAnalysis.elementsWithAlternatives} with alternatives, ${mediaAnalysis.elementsWithoutAlternatives} without, ${mediaAnalysis.decorativeElements} decorative`,
      selector: 'audio, video, embed, iframe',
      elementCount: totalElements,
      affectedSelectors: ['audio', 'video', 'embed', 'iframe'],
      severity: score >= 80 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalElements,
        checkSpecificData: {
          totalMediaElements: mediaAnalysis.totalMediaElements,
          audioOnlyElements: mediaAnalysis.audioOnlyElements.length,
          videoOnlyElements: mediaAnalysis.videoOnlyElements.length,
          elementsWithAlternatives: mediaAnalysis.elementsWithAlternatives,
          elementsWithoutAlternatives: mediaAnalysis.elementsWithoutAlternatives,
          decorativeElements: mediaAnalysis.decorativeElements,
          alternativeRate: (totalElements - mediaAnalysis.decorativeElements) > 0 
            ? (mediaAnalysis.elementsWithAlternatives / (totalElements - mediaAnalysis.decorativeElements) * 100).toFixed(1)
            : '0',
        },
      },
    });

    // Generate recommendations
    if (mediaAnalysis.elementsWithoutAlternatives > 0) {
      recommendations.push('Provide text transcripts for all audio-only content');
      recommendations.push('Provide text descriptions or audio descriptions for video-only content');
      recommendations.push('Use aria-describedby to associate alternatives with media elements');
      recommendations.push('Ensure alternatives are equivalent to the media content');
    } else {
      recommendations.push('Continue providing text alternatives for media content');
      recommendations.push('Test alternatives with screen reader users');
    }

    if (mediaAnalysis.audioOnlyElements.length > 0) {
      recommendations.push('Consider providing both transcript and audio description options');
    }

    if (mediaAnalysis.videoOnlyElements.length > 0) {
      recommendations.push('For video-only content, provide detailed text descriptions');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
